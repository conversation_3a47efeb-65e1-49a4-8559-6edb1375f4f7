#!/usr/bin/env python3
"""
Enhanced script to create a comprehensive 80+ page Word document 
with detailed content for the GDR and ADR study.
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import datetime

def create_enhanced_document():
    """Create enhanced Word document with comprehensive content"""
    
    # Create new document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # Add custom styles
    styles = doc.styles
    
    # Title style
    if 'CustomTitle' not in [s.name for s in styles]:
        title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = 'Times New Roman'
        title_font.size = Pt(18)
        title_font.bold = True
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_after = Pt(12)
    
    # Heading styles
    if 'CustomHeading1' not in [s.name for s in styles]:
        heading1_style = styles.add_style('CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = 'Times New Roman'
        heading1_font.size = Pt(16)
        heading1_font.bold = True
        heading1_style.paragraph_format.space_before = Pt(12)
        heading1_style.paragraph_format.space_after = Pt(6)
    
    if 'CustomHeading2' not in [s.name for s in styles]:
        heading2_style = styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
        heading2_font = heading2_style.font
        heading2_font.name = 'Times New Roman'
        heading2_font.size = Pt(14)
        heading2_font.bold = True
        heading2_style.paragraph_format.space_before = Pt(10)
        heading2_style.paragraph_format.space_after = Pt(4)
    
    # Normal text style
    if 'CustomNormal' not in [s.name for s in styles]:
        normal_style = styles.add_style('CustomNormal', WD_STYLE_TYPE.PARAGRAPH)
        normal_font = normal_style.font
        normal_font.name = 'Times New Roman'
        normal_font.size = Pt(12)
        normal_style.paragraph_format.line_spacing = 1.5
        normal_style.paragraph_format.space_after = Pt(6)
        normal_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    
    return doc

def add_comprehensive_content(doc):
    """Add comprehensive content to ensure 80+ pages"""
    
    # Title Page
    title = doc.add_paragraph()
    title.style = 'CustomTitle'
    title_run = title.add_run("STUDY ON OPERATIONAL AND PRACTICAL DIMENSIONS OF GDR AND ADR ISSUES OF INDIAN COMPANIES")
    title_run.font.size = Pt(20)
    title_run.font.bold = True
    
    doc.add_paragraph("\n\n")
    
    subtitle = doc.add_paragraph("A Comprehensive Analysis of Global and American Depositary Receipts")
    subtitle.style = 'CustomTitle'
    subtitle.runs[0].font.size = Pt(16)
    
    doc.add_paragraph("\n\n\n")
    
    author_info = doc.add_paragraph("Submitted for MBA Program")
    author_info.style = 'CustomTitle'
    author_info.runs[0].font.size = Pt(14)
    author_info.runs[0].font.bold = False
    
    doc.add_paragraph("\n\n")
    
    date_para = doc.add_paragraph(f"Date: {datetime.datetime.now().strftime('%B %Y')}")
    date_para.style = 'CustomTitle'
    date_para.runs[0].font.size = Pt(12)
    date_para.runs[0].font.bold = False
    
    doc.add_page_break()
    
    # Read and process the markdown content
    try:
        with open('GDR_ADR_Study_Document.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split content into sections
        sections = content.split('## ')
        
        for section in sections[1:]:  # Skip the first empty section
            lines = section.split('\n')
            section_title = lines[0].strip()
            
            # Add section heading
            heading = doc.add_paragraph(section_title)
            heading.style = 'CustomHeading1'
            
            # Process section content
            current_paragraph = ""
            for line in lines[1:]:
                line = line.strip()
                
                if not line:
                    if current_paragraph:
                        para = doc.add_paragraph(current_paragraph)
                        para.style = 'CustomNormal'
                        current_paragraph = ""
                    continue
                
                if line.startswith('### '):
                    if current_paragraph:
                        para = doc.add_paragraph(current_paragraph)
                        para.style = 'CustomNormal'
                        current_paragraph = ""
                    
                    subheading = doc.add_paragraph(line[4:])
                    subheading.style = 'CustomHeading2'
                    
                elif line.startswith('**') and line.endswith('**'):
                    if current_paragraph:
                        para = doc.add_paragraph(current_paragraph)
                        para.style = 'CustomNormal'
                        current_paragraph = ""
                    
                    bold_heading = doc.add_paragraph(line[2:-2])
                    bold_heading.style = 'CustomHeading2'
                    
                elif line.startswith('---'):
                    if current_paragraph:
                        para = doc.add_paragraph(current_paragraph)
                        para.style = 'CustomNormal'
                        current_paragraph = ""
                    doc.add_page_break()
                    
                elif not line.startswith('#') and not line.startswith('*[Document'):
                    if current_paragraph:
                        current_paragraph += " " + line
                    else:
                        current_paragraph = line
            
            # Add final paragraph if exists
            if current_paragraph:
                para = doc.add_paragraph(current_paragraph)
                para.style = 'CustomNormal'
    
    except FileNotFoundError:
        print("Markdown file not found, creating basic structure...")
        
        # Add basic content structure
        sections = [
            "Executive Summary",
            "Introduction", 
            "Literature Survey",
            "Research Methodology",
            "Analysis of GDR Issues",
            "Analysis of ADR Issues", 
            "Comparative Analysis",
            "Results and Discussion",
            "Conclusion",
            "References"
        ]
        
        for section in sections:
            heading = doc.add_paragraph(section)
            heading.style = 'CustomHeading1'
            
            # Add placeholder content
            for i in range(3):
                para_text = f"This section provides comprehensive analysis of {section.lower()}. The detailed examination covers multiple dimensions including operational challenges, regulatory requirements, strategic implications, and practical considerations for Indian companies pursuing international capital market access through depositary receipt programs. The analysis is based on extensive research and case studies of prominent Indian companies with successful international listings."
                para = doc.add_paragraph(para_text)
                para.style = 'CustomNormal'
    
    return doc

def add_detailed_tables_and_analysis(doc):
    """Add detailed tables and additional analysis to reach 80+ pages"""
    
    # Add comprehensive tables section
    heading = doc.add_paragraph("DETAILED ANALYSIS TABLES AND FRAMEWORKS")
    heading.style = 'CustomHeading1'
    
    # Table 1: Regulatory Comparison
    table_heading = doc.add_paragraph("Table 1: Comprehensive Regulatory Framework Comparison")
    table_heading.style = 'CustomHeading2'
    
    table = doc.add_table(rows=1, cols=3)
    table.style = 'Table Grid'
    
    # Header row
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Aspect'
    hdr_cells[1].text = 'GDR Requirements'
    hdr_cells[2].text = 'ADR Requirements'
    
    # Add data rows
    comparison_data = [
        ('Regulatory Authority', 'European Securities Regulators', 'SEC (United States)'),
        ('Approval Timeline', '4-6 months', '6-12 months'),
        ('Initial Costs', '$500K - $1.5M', '$1M - $3M'),
        ('Annual Compliance Costs', '$300K - $800K', '$800K - $2M'),
        ('Reporting Requirements', 'European Standards', 'US GAAP + SOX'),
        ('Corporate Governance', 'European Standards', 'US Standards + SOX'),
        ('Disclosure Obligations', 'Moderate', 'Extensive'),
        ('Audit Requirements', 'Standard', 'Enhanced (SOX)'),
        ('Internal Controls', 'Basic', 'Comprehensive (SOX 404)'),
        ('Investor Relations', 'European Focus', 'US Focus'),
    ]
    
    for aspect, gdr_req, adr_req in comparison_data:
        row_cells = table.add_row().cells
        row_cells[0].text = aspect
        row_cells[1].text = gdr_req
        row_cells[2].text = adr_req
    
    # Add analysis paragraphs
    analysis_paragraphs = [
        "The regulatory framework comparison reveals significant differences in complexity and resource requirements between GDR and ADR programs. These differences have substantial implications for Indian companies' strategic decision-making and operational planning.",
        
        "The timeline differences reflect the varying complexity of approval processes, with ADR programs requiring more extensive documentation and regulatory review. Companies must factor these timelines into their capital raising and strategic planning processes.",
        
        "Cost differentials between GDR and ADR programs are substantial, with ADR programs typically requiring 2-3 times higher investment in both initial setup and ongoing compliance. These cost differences must be weighed against the potential benefits of accessing larger and more liquid capital markets.",
        
        "The reporting and compliance requirements for ADR programs are significantly more extensive, particularly following the implementation of the Sarbanes-Oxley Act. Companies must develop robust internal control frameworks and enhanced governance practices to meet these requirements.",
        
        "Corporate governance requirements for ADR programs often exceed domestic Indian standards and may require significant changes to board composition, committee structures, and governance procedures. Companies must carefully assess their readiness to adopt these enhanced governance practices."
    ]
    
    for para_text in analysis_paragraphs:
        para = doc.add_paragraph(para_text)
        para.style = 'CustomNormal'
    
    return doc

def main():
    """Main function to create enhanced Word document"""
    
    print("Creating enhanced GDR and ADR study document...")
    
    # Create document
    doc = create_enhanced_document()
    
    # Add comprehensive content
    doc = add_comprehensive_content(doc)
    
    # Add detailed analysis and tables
    doc = add_detailed_tables_and_analysis(doc)
    
    # Save the document
    filename = "GDR_ADR_Study_Enhanced_Complete.docx"
    doc.save(filename)
    
    # Calculate statistics
    total_paragraphs = len(doc.paragraphs)
    word_count = sum(len(para.text.split()) for para in doc.paragraphs)
    estimated_pages = word_count // 250
    
    print(f"Enhanced document created successfully: {filename}")
    print(f"Total paragraphs: {total_paragraphs}")
    print(f"Word count: {word_count:,}")
    print(f"Estimated pages: {estimated_pages}")
    
    if estimated_pages >= 80:
        print("✅ Document meets 80+ page requirement!")
    else:
        print(f"⚠️  Document is {80 - estimated_pages} pages short of 80-page target")

if __name__ == "__main__":
    main()
