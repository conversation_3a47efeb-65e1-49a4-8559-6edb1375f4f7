#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to convert the GDR and ADR study markdown document to Word format.
This will create a properly formatted Word document with styles and formatting.
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import re

def create_word_document():
    """Convert markdown content to Word document"""
    
    # Create new document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # Add custom styles
    styles = doc.styles
    
    # Title style
    if 'CustomTitle' not in [s.name for s in styles]:
        title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = 'Times New Roman'
        title_font.size = Pt(18)
        title_font.bold = True
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_after = Pt(12)
    
    # Heading styles
    if 'CustomHeading1' not in [s.name for s in styles]:
        heading1_style = styles.add_style('CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = 'Times New Roman'
        heading1_font.size = Pt(16)
        heading1_font.bold = True
        heading1_style.paragraph_format.space_before = Pt(12)
        heading1_style.paragraph_format.space_after = Pt(6)
    
    if 'CustomHeading2' not in [s.name for s in styles]:
        heading2_style = styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
        heading2_font = heading2_style.font
        heading2_font.name = 'Times New Roman'
        heading2_font.size = Pt(14)
        heading2_font.bold = True
        heading2_style.paragraph_format.space_before = Pt(10)
        heading2_style.paragraph_format.space_after = Pt(4)
    
    if 'CustomHeading3' not in [s.name for s in styles]:
        heading3_style = styles.add_style('CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
        heading3_font = heading3_style.font
        heading3_font.name = 'Times New Roman'
        heading3_font.size = Pt(12)
        heading3_font.bold = True
        heading3_style.paragraph_format.space_before = Pt(8)
        heading3_style.paragraph_format.space_after = Pt(4)
    
    # Normal text style
    if 'CustomNormal' not in [s.name for s in styles]:
        normal_style = styles.add_style('CustomNormal', WD_STYLE_TYPE.PARAGRAPH)
        normal_font = normal_style.font
        normal_font.name = 'Times New Roman'
        normal_font.size = Pt(12)
        normal_style.paragraph_format.line_spacing = 1.5
        normal_style.paragraph_format.space_after = Pt(6)
        normal_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    
    return doc

def process_markdown_content(doc, content):
    """Process markdown content and add to Word document"""
    
    lines = content.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
        
        # Main title (single #)
        if line.startswith('# ') and not line.startswith('## '):
            title_text = line[2:].strip()
            para = doc.add_paragraph(title_text)
            para.style = 'CustomTitle'
            
        # Section headings (##)
        elif line.startswith('## ') and not line.startswith('### '):
            heading_text = line[3:].strip()
            para = doc.add_paragraph(heading_text)
            para.style = 'CustomHeading1'
            
        # Subsection headings (###)
        elif line.startswith('### '):
            heading_text = line[4:].strip()
            para = doc.add_paragraph(heading_text)
            para.style = 'CustomHeading2'
            
        # Bold text headings (**text**)
        elif line.startswith('**') and line.endswith('**') and len(line) < 100:
            heading_text = line[2:-2].strip()
            para = doc.add_paragraph(heading_text)
            para.style = 'CustomHeading3'
            
        # Horizontal rules
        elif line.startswith('---'):
            doc.add_page_break()
            
        # Regular paragraphs
        else:
            # Handle multi-line paragraphs
            paragraph_lines = [line]
            j = i + 1
            
            # Collect continuation lines
            while j < len(lines):
                next_line = lines[j].strip()
                if (not next_line or 
                    next_line.startswith('#') or 
                    next_line.startswith('**') or 
                    next_line.startswith('---') or
                    next_line.startswith('*[Document')):
                    break
                paragraph_lines.append(next_line)
                j += 1
            
            # Join paragraph lines
            paragraph_text = ' '.join(paragraph_lines).strip()
            
            if paragraph_text and not paragraph_text.startswith('*[Document'):
                para = doc.add_paragraph(paragraph_text)
                para.style = 'CustomNormal'
            
            i = j - 1
        
        i += 1
    
    return doc

def main():
    """Main function to convert markdown to Word"""
    
    try:
        # Read the markdown file
        with open('GDR_ADR_Study_Document.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("Reading markdown file...")
        
        # Create Word document
        doc = create_word_document()
        print("Created Word document structure...")
        
        # Process content
        doc = process_markdown_content(doc, content)
        print("Processed markdown content...")
        
        # Save the document
        filename = "GDR_ADR_Study_Complete_Final.docx"
        doc.save(filename)
        print(f"Document saved successfully: {filename}")
        print("Conversion completed!")
        
        # Print document statistics
        total_paragraphs = len(doc.paragraphs)
        print(f"Total paragraphs: {total_paragraphs}")
        
        # Estimate word count
        word_count = 0
        for para in doc.paragraphs:
            word_count += len(para.text.split())
        
        print(f"Estimated word count: {word_count:,}")
        print(f"Estimated page count: {word_count // 250} pages")
        
    except FileNotFoundError:
        print("Error: GDR_ADR_Study_Document.md file not found!")
        print("Please ensure the markdown file exists in the current directory.")
    except Exception as e:
        print(f"Error during conversion: {e}")
        print("Please check the file and try again.")

if __name__ == "__main__":
    main()
