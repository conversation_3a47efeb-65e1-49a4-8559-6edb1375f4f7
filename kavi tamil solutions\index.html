<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>Kavi Tamil Solutions - Embedded Systems, ML Training, Data Engineering & College Projects</title>
    <meta name="description" content="Kavi Tamil Solutions offers cutting-edge technology solutions including embedded systems, IoT projects, machine learning training, data engineering services, and college project assistance. Expert guidance for students and professionals.">
    <meta name="keywords" content="embedded systems, IoT projects, machine learning training, data engineering, college projects, Arduino, ESP32, Python, AI solutions, automation, smart home, robotics">
    <meta name="author" content="Kavi Tamil Solutions">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="Kavi Tamil Solutions - Technology Solutions & Training">
    <meta property="og:description" content="Expert technology solutions in embedded systems, machine learning, data engineering, and academic project guidance.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yourusername.github.io/kavi-tamil-solutions/">
    <meta property="og:image" content="https://yourusername.github.io/kavi-tamil-solutions/og-image.jpg">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Kavi Tamil Solutions - Technology Solutions & Training">
    <meta name="twitter:description" content="Expert technology solutions in embedded systems, machine learning, data engineering, and academic project guidance.">
    <meta name="twitter:image" content="https://yourusername.github.io/kavi-tamil-solutions/twitter-image.jpg">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://yourusername.github.io/kavi-tamil-solutions/">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">

    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Embedded CSS -->
    <style>
        /* Custom CSS for Kavi Tamil Solutions */
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
            --brutalist-shadow: 8px 8px 0px rgba(0, 0, 0, 0.8);
            --brutalist-border: 4px solid #000;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        /* Brutalist Components */
        .brutalist-text {
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 3px 3px 0px rgba(0, 0, 0, 0.3);
        }

        .brutalist-btn {
            border: 3px solid #000 !important;
            box-shadow: 6px 6px 0px rgba(0, 0, 0, 0.8) !important;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .brutalist-btn:hover {
            transform: translate(3px, 3px);
            box-shadow: 3px 3px 0px rgba(0, 0, 0, 0.8) !important;
        }

        .brutalist-card {
            border: 3px solid #000 !important;
            box-shadow: var(--brutalist-shadow);
            background: #fff;
            transition: all 0.3s ease;
        }

        .brutalist-card:hover {
            transform: translate(-4px, -4px);
            box-shadow: 12px 12px 0px rgba(0, 0, 0, 0.8);
        }

        .brutalist-underline {
            width: 100px;
            height: 6px;
            background: var(--primary-color);
            margin: 0 auto 20px;
            border: 2px solid #000;
        }

        .brutalist-underline-light {
            width: 100px;
            height: 6px;
            background: #fff;
            margin: 0 auto 20px;
            border: 2px solid #fff;
        }

        /* Navigation */
        .navbar {
            backdrop-filter: blur(10px);
            background: rgba(52, 58, 64, 0.95) !important;
            border-bottom: 3px solid var(--primary-color);
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-link {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-visual {
            position: relative;
            height: 400px;
        }

        .floating-card {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            animation: float 6s ease-in-out infinite;
        }

        .floating-card:nth-child(1) {
            top: 50px;
            right: 100px;
            animation-delay: 0s;
        }

        .floating-card:nth-child(2) {
            top: 150px;
            right: 200px;
            animation-delay: 2s;
        }

        .floating-card:nth-child(3) {
            top: 250px;
            right: 50px;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Project Cards */
        .project-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            height: 100%;
            position: relative;
        }

        .card-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            border: 3px solid #000;
        }

        .card-icon i {
            font-size: 2rem;
            color: white;
        }

        /* Training and Projects */
        .training-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            border: 3px solid #000;
            box-shadow: var(--brutalist-shadow);
        }

        .projects-showcase {
            background: var(--light-color);
            padding: 40px;
            border-radius: 15px;
            border: 3px solid #000;
            box-shadow: var(--brutalist-shadow);
        }

        .project-item {
            padding: 15px;
            background: white;
            border-left: 4px solid var(--primary-color);
            margin-bottom: 15px;
            border-radius: 5px;
        }

        /* Service Cards */
        .service-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #000;
            box-shadow: 4px 4px 0px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            height: 100%;
        }

        .service-card:hover {
            transform: translate(-2px, -2px);
            box-shadow: 6px 6px 0px rgba(0, 0, 0, 0.3);
        }

        /* College Projects */
        .college-project-card {
            background: white;
            border: 3px solid #000;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--brutalist-shadow);
            height: 100%;
        }

        .project-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid #000;
        }

        .project-list {
            list-style: none;
            padding: 20px;
        }

        .project-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 20px;
        }

        .project-list li:before {
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--primary-color);
            font-weight: bold;
        }

        .project-list li:last-child {
            border-bottom: none;
        }

        /* Contact Info */
        .contact-info {
            padding: 20px;
        }

        .contact-item {
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            height: 100%;
        }

        .contact-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .social-links .btn {
            border: 2px solid rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .social-links .btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        /* WhatsApp Button Styles */
        .whatsapp-btn {
            background: #25D366 !important;
            border-color: #25D366 !important;
            color: white !important;
            box-shadow: 6px 6px 0px rgba(37, 211, 102, 0.3) !important;
        }

        .whatsapp-btn:hover {
            background: #128C7E !important;
            border-color: #128C7E !important;
            box-shadow: 3px 3px 0px rgba(37, 211, 102, 0.3) !important;
        }

        /* Floating WhatsApp Button */
        .floating-whatsapp {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            width: 60px;
            height: 60px;
            background: #25D366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
            transition: all 0.3s ease;
            text-decoration: none;
            border: 3px solid #fff;
        }

        .floating-whatsapp:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
            background: #128C7E;
        }

        .floating-whatsapp i {
            font-size: 24px;
            color: white;
        }

        .floating-whatsapp::before {
            content: 'Chat with us!';
            position: absolute;
            right: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .floating-whatsapp:hover::before {
            opacity: 1;
            visibility: visible;
            right: 75px;
        }

        /* Pulse animation for floating button */
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        .floating-whatsapp {
            animation: pulse 2s infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-visual {
                display: none;
            }

            .floating-card {
                position: static;
                margin: 10px 0;
                animation: none;
            }

            .brutalist-btn {
                width: 100%;
                margin-bottom: 10px;
            }

            .project-card,
            .training-card,
            .projects-showcase,
            .service-card,
            .college-project-card {
                margin-bottom: 20px;
            }
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Section Spacing */
        section {
            scroll-margin-top: 80px;
        }

        /* Custom Animations */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            animation: slideInUp 0.6s ease-out;
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Utility Classes */
        .text-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .border-brutalist {
            border: 3px solid #000 !important;
        }

        .shadow-brutalist {
            box-shadow: var(--brutalist-shadow) !important;
        }

        /* Additional styles for better SEO and accessibility */
        .navbar.scrolled {
            background: rgba(52, 58, 64, 0.98) !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-link.active {
            color: var(--primary-color) !important;
            font-weight: bold;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#home">
                <i class="fas fa-code me-2"></i>Kavi Tamil Solutions
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#embedded">Embedded Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#ml">ML Training</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#data-engineering">Data Engineering</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#college">College Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="display-3 fw-bold mb-4 brutalist-text">
                            Kavi Tamil Solutions
                        </h1>
                        <p class="lead mb-4">Innovative technology solutions spanning embedded systems, machine learning, data engineering, and academic excellence.</p>
                        <div class="hero-buttons">
                            <a href="#embedded" class="btn btn-primary btn-lg me-3 brutalist-btn">Explore Projects</a>
                            <a href="https://wa.me/************?text=Hi%20Kavi%20Tamil%20Solutions!%20I'm%20interested%20in%20your%20services."
                               class="btn btn-success btn-lg me-3 whatsapp-btn brutalist-btn" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp Us
                            </a>
                            <a href="#contact" class="btn btn-outline-light btn-lg">Get In Touch</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-visual">
                        <div class="floating-card">
                            <i class="fas fa-microchip fa-3x text-primary mb-3"></i>
                            <h5>Embedded Systems</h5>
                        </div>
                        <div class="floating-card">
                            <i class="fas fa-brain fa-3x text-success mb-3"></i>
                            <h5>Machine Learning</h5>
                        </div>
                        <div class="floating-card">
                            <i class="fas fa-database fa-3x text-warning mb-3"></i>
                            <h5>Data Engineering</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Embedded Projects Section -->
    <section id="embedded" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-4 fw-bold">Embedded Projects</h2>
                    <div class="brutalist-underline"></div>
                    <p class="lead">Cutting-edge embedded systems and IoT solutions</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-4">
                    <div class="project-card brutalist-card">
                        <div class="card-icon">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <h5>IoT Monitoring System</h5>
                        <p>Real-time sensor data collection and monitoring using ESP32 and cloud integration.</p>
                        <a href="#" class="btn btn-outline-primary">View Details</a>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="project-card brutalist-card">
                        <div class="card-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h5>Autonomous Robot</h5>
                        <p>Arduino-based autonomous navigation robot with obstacle avoidance capabilities.</p>
                        <a href="#" class="btn btn-outline-primary">View Details</a>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4">
                    <div class="project-card brutalist-card">
                        <div class="card-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <h5>Smart Home System</h5>
                        <p>Complete home automation solution with mobile app control and voice commands.</p>
                        <a href="#" class="btn btn-outline-primary">View Details</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ML Training Section -->
    <section id="ml" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-4 fw-bold">ML Training & Projects</h2>
                    <div class="brutalist-underline"></div>
                    <p class="lead">Machine Learning expertise and innovative AI solutions</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-6">
                    <div class="training-card">
                        <h4 class="mb-3">Training Programs</h4>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Python for Data Science</li>
                            <li><i class="fas fa-check text-success me-2"></i>Machine Learning Fundamentals</li>
                            <li><i class="fas fa-check text-success me-2"></i>Deep Learning with TensorFlow</li>
                            <li><i class="fas fa-check text-success me-2"></i>Computer Vision Projects</li>
                            <li><i class="fas fa-check text-success me-2"></i>Natural Language Processing</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="projects-showcase">
                        <h4 class="mb-3">Featured Projects</h4>
                        <div class="project-item mb-3">
                            <h6>Image Classification System</h6>
                            <p class="text-muted">CNN-based image recognition with 95% accuracy</p>
                        </div>
                        <div class="project-item mb-3">
                            <h6>Predictive Analytics Dashboard</h6>
                            <p class="text-muted">Real-time business intelligence and forecasting</p>
                        </div>
                        <div class="project-item mb-3">
                            <h6>Chatbot Development</h6>
                            <p class="text-muted">NLP-powered customer service automation</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Data Engineering Section -->
    <section id="data-engineering" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-4 fw-bold">Data Engineering</h2>
                    <div class="brutalist-underline"></div>
                    <p class="lead">Robust data pipelines and analytics solutions</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="service-card text-center">
                        <i class="fas fa-database fa-3x text-primary mb-3"></i>
                        <h5>Data Pipeline Design</h5>
                        <p>ETL/ELT processes for efficient data flow</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="service-card text-center">
                        <i class="fas fa-cloud fa-3x text-info mb-3"></i>
                        <h5>Cloud Integration</h5>
                        <p>AWS, Azure, and GCP data solutions</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="service-card text-center">
                        <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                        <h5>Analytics Platforms</h5>
                        <p>Business intelligence and reporting</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="service-card text-center">
                        <i class="fas fa-cogs fa-3x text-warning mb-3"></i>
                        <h5>Automation</h5>
                        <p>Workflow orchestration and monitoring</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- College Projects Section -->
    <section id="college" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-4 fw-bold">College Projects</h2>
                    <div class="brutalist-underline"></div>
                    <p class="lead">Academic excellence and innovative student solutions</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="college-project-card">
                        <div class="project-header">
                            <h5>Final Year Projects</h5>
                        </div>
                        <ul class="project-list">
                            <li>AI-powered Healthcare System</li>
                            <li>Blockchain Voting Platform</li>
                            <li>Smart Campus Management</li>
                            <li>Renewable Energy Monitor</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="college-project-card">
                        <div class="project-header">
                            <h5>Research Projects</h5>
                        </div>
                        <ul class="project-list">
                            <li>Machine Learning Optimization</li>
                            <li>IoT Security Framework</li>
                            <li>Data Mining Algorithms</li>
                            <li>Computer Vision Applications</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="college-project-card">
                        <div class="project-header">
                            <h5>Mini Projects</h5>
                        </div>
                        <ul class="project-list">
                            <li>Web Development Portfolio</li>
                            <li>Mobile App Prototypes</li>
                            <li>Database Management Systems</li>
                            <li>Network Security Tools</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 bg-dark text-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-4 fw-bold">Get In Touch</h2>
                    <div class="brutalist-underline-light"></div>
                    <p class="lead">Ready to start your next project?</p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <div class="contact-info">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="contact-item">
                                    <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                                    <h5>Email</h5>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="contact-item">
                                    <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                                    <h5>Phone</h5>
                                    <p>+91 98765 43210</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                                    <h5>Location</h5>
                                    <p>Tamil Nadu, India</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-5">
                            <h4 class="mb-4">Connect With Us</h4>
                            <div class="social-links">
                                <!-- Primary WhatsApp Button -->
                                <a href="https://wa.me/************?text=Hi%20Kavi%20Tamil%20Solutions!%20I'm%20interested%20in%20your%20services."
                                   class="btn btn-success btn-lg me-3 mb-3 whatsapp-btn brutalist-btn" target="_blank">
                                    <i class="fab fa-whatsapp me-2"></i>Chat on WhatsApp
                                </a>
                                <a href="mailto:<EMAIL>" class="btn btn-outline-light btn-lg me-3 mb-3">
                                    <i class="fas fa-envelope me-2"></i>Send Email
                                </a>
                                <a href="tel:+************" class="btn btn-outline-light btn-lg mb-3">
                                    <i class="fas fa-phone me-2"></i>Call Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/************?text=Hi%20Kavi%20Tamil%20Solutions!%20I'm%20interested%20in%20your%20services."
       class="floating-whatsapp" target="_blank" aria-label="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 Kavi Tamil Solutions. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light me-3"><i class="fab fa-linkedin"></i></a>
                    <a href="#" class="text-light me-3"><i class="fab fa-github"></i></a>
                    <a href="#" class="text-light"><i class="fab fa-twitter"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Kavi Tamil Solutions",
        "description": "Technology solutions provider specializing in embedded systems, machine learning training, data engineering, and college project assistance",
        "url": "https://yourusername.github.io/kavi-tamil-solutions/",
        "logo": "https://yourusername.github.io/kavi-tamil-solutions/logo.png",
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["English", "Tamil"]
        },
        "areaServed": "Global",
        "serviceType": [
            "Embedded Systems Development",
            "IoT Solutions",
            "Machine Learning Training",
            "Data Engineering Services",
            "College Project Assistance",
            "Arduino Programming",
            "ESP32 Development",
            "Python Training",
            "AI Solutions"
        ]
    }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Embedded JavaScript -->
    <script>
        // JavaScript for Kavi Tamil Solutions Website
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all functionality
            initSmoothScrolling();
            initScrollAnimations();
            initNavbarEffects();
            initProjectCards();
        });

        // Smooth scrolling for navigation links
        function initSmoothScrolling() {
            const navLinks = document.querySelectorAll('a[href^="#"]');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);

                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar

                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // Scroll animations
        function initScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-on-scroll');
                    }
                });
            }, observerOptions);

            // Observe all cards and sections
            const elementsToAnimate = document.querySelectorAll(
                '.project-card, .service-card, .college-project-card, .training-card, .projects-showcase'
            );

            elementsToAnimate.forEach(element => {
                observer.observe(element);
            });
        }

        // Navbar effects
        function initNavbarEffects() {
            const navbar = document.querySelector('.navbar');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Active navigation highlighting
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            window.addEventListener('scroll', function() {
                let current = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.clientHeight;

                    if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });
            });
        }

        // Form handling - Works with Formspree or fallback to mailto
        function initFormHandling() {
            const contactForm = document.querySelector('#contact form');

            if (contactForm) {
                // Check if using Formspree (has action attribute)
                const isFormspree = contactForm.hasAttribute('action') &&
                                  contactForm.getAttribute('action').includes('formspree.io');

                if (isFormspree) {
                    // Formspree handling - let the form submit naturally
                    contactForm.addEventListener('submit', function(e) {
                        const submitBtn = this.querySelector('button[type="submit"]');
                        const originalText = submitBtn.innerHTML;

                        // Show loading state
                        submitBtn.innerHTML = '<span class="loading"></span> Sending...';
                        submitBtn.disabled = true;

                        // Let Formspree handle the submission
                        // The form will redirect or show success message
                    });
                } else {
                    // Fallback to mailto for development/testing
                    contactForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        const submitBtn = this.querySelector('button[type="submit"]');
                        const originalText = submitBtn.innerHTML;

                        // Show loading state
                        submitBtn.innerHTML = '<span class="loading"></span> Preparing...';
                        submitBtn.disabled = true;

                        // Get form data
                        const name = this.querySelector('input[name="name"]').value;
                        const email = this.querySelector('input[name="email"]').value;
                        const subject = this.querySelector('input[name="subject"]').value;
                        const message = this.querySelector('textarea[name="message"]').value;

                        // Create mailto link
                        const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(`Name: ${name}\nEmail: ${email}\n\nMessage:\n${message}`)}`;

                        setTimeout(() => {
                            submitBtn.innerHTML = '<i class="fas fa-envelope"></i> Opening Email...';
                            submitBtn.classList.remove('btn-primary');
                            submitBtn.classList.add('btn-info');

                            // Open email client
                            window.location.href = mailtoLink;

                            // Reset form and button
                            setTimeout(() => {
                                this.reset();
                                submitBtn.innerHTML = originalText;
                                submitBtn.disabled = false;
                                submitBtn.classList.remove('btn-info');
                                submitBtn.classList.add('btn-primary');
                            }, 2000);
                        }, 1000);
                    });
                }

                // Check for success parameter in URL (Formspree redirect)
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.get('success') === 'true') {
                    showSuccessMessage();
                }
            }
        }

        // Show success message after form submission
        function showSuccessMessage() {
            const contactSection = document.querySelector('#contact');
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success mt-4';
            successDiv.innerHTML = `
                <h5><i class="fas fa-check-circle"></i> Message Sent Successfully!</h5>
                <p>Thank you for contacting us. We'll get back to you soon.</p>
            `;

            const form = contactSection.querySelector('form');
            form.parentNode.insertBefore(successDiv, form);

            // Remove success message after 5 seconds
            setTimeout(() => {
                successDiv.remove();
                // Remove success parameter from URL
                const url = new URL(window.location);
                url.searchParams.delete('success');
                window.history.replaceState({}, document.title, url);
            }, 5000);
        }

        // Project card interactions
        function initProjectCards() {
            const projectCards = document.querySelectorAll('.project-card, .service-card');

            projectCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translate(-4px, -4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translate(0, 0) scale(1)';
                });
            });

            // Add click effects to brutalist buttons
            const brutalistBtns = document.querySelectorAll('.brutalist-btn');

            brutalistBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // Utility functions
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Performance optimization
        const debouncedScroll = debounce(() => {
            // Any expensive scroll operations can go here
        }, 10);

        window.addEventListener('scroll', debouncedScroll);

        // Console welcome message
        console.log(`
            ╔══════════════════════════════════════╗
            ║        Kavi Tamil Solutions          ║
            ║     Modern Web Development           ║
            ║     Built with ❤️ and Code          ║
            ╚══════════════════════════════════════╝
        `);
    </script>
</body>
</html>
